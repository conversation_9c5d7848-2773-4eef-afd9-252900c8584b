[{"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx": "5", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\compositions\\OrganicComposition.tsx": "6", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\CriticalCSS.tsx": "7", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Footer.tsx": "8", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Header.tsx": "9", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\PageWrapper.tsx": "10", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\PerformanceMonitor.tsx": "11", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\AnimatedShapes.tsx": "12", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Circle.tsx": "13", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\HalfCircle.tsx": "14", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Rectangle.tsx": "15", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\RoundedShapes.tsx": "16", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Triangle.tsx": "17", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\hooks\\useScrollAnimation.ts": "18", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\middleware.ts": "19"}, {"size": 3945, "mtime": 1751368348484, "results": "20", "hashOfConfig": "21"}, {"size": 5800, "mtime": 1751368397355, "results": "22", "hashOfConfig": "21"}, {"size": 1923, "mtime": 1751546500105, "results": "23", "hashOfConfig": "21"}, {"size": 46447, "mtime": 1751545837760, "results": "24", "hashOfConfig": "21"}, {"size": 5956, "mtime": 1751366727918, "results": "25", "hashOfConfig": "21"}, {"size": 7017, "mtime": 1751465060420, "results": "26", "hashOfConfig": "21"}, {"size": 2334, "mtime": 1751546122736, "results": "27", "hashOfConfig": "21"}, {"size": 1338, "mtime": 1751365816717, "results": "28", "hashOfConfig": "21"}, {"size": 3248, "mtime": 1751400818384, "results": "29", "hashOfConfig": "21"}, {"size": 419, "mtime": 1751363944301, "results": "30", "hashOfConfig": "21"}, {"size": 1730, "mtime": 1751546231864, "results": "31", "hashOfConfig": "21"}, {"size": 6976, "mtime": 1751403724820, "results": "32", "hashOfConfig": "21"}, {"size": 949, "mtime": 1751366318919, "results": "33", "hashOfConfig": "21"}, {"size": 1302, "mtime": 1751363912836, "results": "34", "hashOfConfig": "21"}, {"size": 1411, "mtime": 1751366358877, "results": "35", "hashOfConfig": "21"}, {"size": 3823, "mtime": 1751402711577, "results": "36", "hashOfConfig": "21"}, {"size": 2642, "mtime": 1751365661262, "results": "37", "hashOfConfig": "21"}, {"size": 4067, "mtime": 1751465134724, "results": "38", "hashOfConfig": "21"}, {"size": 1945, "mtime": 1751546184983, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dzgleq", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\page.tsx", ["97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108"], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\compositions\\OrganicComposition.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\CriticalCSS.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\PageWrapper.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\PerformanceMonitor.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\AnimatedShapes.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Circle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\HalfCircle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Rectangle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\RoundedShapes.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Triangle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\hooks\\useScrollAnimation.ts", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\middleware.ts", [], [], {"ruleId": "109", "severity": 1, "message": "110", "line": 422, "column": 29, "nodeType": "111", "endLine": 422, "endColumn": 248}, {"ruleId": "109", "severity": 1, "message": "110", "line": 436, "column": 29, "nodeType": "111", "endLine": 436, "endColumn": 238}, {"ruleId": "109", "severity": 1, "message": "110", "line": 450, "column": 29, "nodeType": "111", "endLine": 450, "endColumn": 236}, {"ruleId": "109", "severity": 1, "message": "110", "line": 464, "column": 29, "nodeType": "111", "endLine": 464, "endColumn": 248}, {"ruleId": "109", "severity": 1, "message": "110", "line": 488, "column": 29, "nodeType": "111", "endLine": 488, "endColumn": 250}, {"ruleId": "109", "severity": 1, "message": "110", "line": 502, "column": 29, "nodeType": "111", "endLine": 502, "endColumn": 250}, {"ruleId": "109", "severity": 1, "message": "110", "line": 516, "column": 29, "nodeType": "111", "endLine": 516, "endColumn": 244}, {"ruleId": "109", "severity": 1, "message": "110", "line": 530, "column": 29, "nodeType": "111", "endLine": 530, "endColumn": 238}, {"ruleId": "109", "severity": 1, "message": "110", "line": 558, "column": 29, "nodeType": "111", "endLine": 558, "endColumn": 240}, {"ruleId": "109", "severity": 1, "message": "110", "line": 572, "column": 29, "nodeType": "111", "endLine": 572, "endColumn": 241}, {"ruleId": "109", "severity": 1, "message": "110", "line": 596, "column": 29, "nodeType": "111", "endLine": 596, "endColumn": 236}, {"ruleId": "109", "severity": 1, "message": "110", "line": 610, "column": 29, "nodeType": "111", "endLine": 610, "endColumn": 240}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]