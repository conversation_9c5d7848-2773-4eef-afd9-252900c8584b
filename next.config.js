/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  // Optimize CSS and performance
  compiler: {
    // Remove console.logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Optimize images and static assets
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 31536000, // 1 year
  },
  // Configure headers for better caching and performance
  async headers() {
    const isProduction = process.env.NODE_ENV === 'production'

    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
        ],
      },
      // Cache static assets aggressively in production
      {
        source: '/images/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: isProduction
              ? 'public, max-age=31536000, immutable'
              : 'public, max-age=0, must-revalidate'
          },
          {
            key: 'Vary',
            value: 'Accept-Encoding'
          },
        ],
      },
      // Cache CSS and JS files with versioning
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: isProduction
              ? 'public, max-age=31536000, immutable'
              : 'public, max-age=0, must-revalidate'
          },
          {
            key: 'Vary',
            value: 'Accept-Encoding'
          },
        ],
      },
      // Cache fonts with long expiry
      {
        source: '/fonts/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: isProduction
              ? 'public, max-age=31536000, immutable'
              : 'public, max-age=86400'
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          },
        ],
      },
      // Cache API responses with shorter TTL
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: isProduction
              ? 'public, max-age=3600, stale-while-revalidate=86400'
              : 'no-cache, no-store, must-revalidate'
          },
        ],
      },
      // HTML pages - optimized for bfcache
      {
        source: '/((?!api|_next|images|fonts).*)',
        headers: [
          {
            key: 'Cache-Control',
            value: isProduction
              ? 'public, max-age=0, must-revalidate, stale-while-revalidate=86400'
              : 'no-cache, no-store, must-revalidate'
          },
        ],
      },
    ]
  },
  // Webpack optimizations
  webpack: (config, { dev, isServer }) => {
    // Optimize CSS in production
    if (!dev && !isServer) {
      config.optimization.splitChunks.cacheGroups.styles = {
        name: 'styles',
        test: /\.(css|scss|sass)$/,
        chunks: 'all',
        enforce: true,
      }
    }

    return config
  },
}

module.exports = nextConfig
