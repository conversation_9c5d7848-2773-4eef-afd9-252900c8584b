(()=>{var e={};e.id=534,e.ids=[534],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},5335:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>b,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(482),a=t(9108),l=t(2563),o=t.n(l),i=t(8300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let c=["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9975)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,488)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx"],u="/work/page",b={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},430:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1900,23)),Promise.resolve().then(t.bind(t,6454)),Promise.resolve().then(t.bind(t,9705))},9975:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(5036),a=t(7621),l=t(1843),o=t(3956),i=t(6193);let n={sm:"w-8 h-4",md:"w-16 h-8",lg:"w-24 h-12",xl:"w-32 h-16"},c={red:"bg-bauhaus-red",yellow:"bg-bauhaus-yellow",blue:"bg-bauhaus-blue",black:"bg-bauhaus-black",white:"bg-bauhaus-white border border-bauhaus-black"},d={top:"rounded-t-full",bottom:"rounded-b-full",left:"rounded-l-full",right:"rounded-r-full"},u=e=>{switch(e){case"left":case"right":return"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32";default:return""}};function b({size:e="md",color:r="red",direction:t="top",className:a=""}){let l=u(t)||n[e];return s.jsx("div",{className:`${l} ${c[r]} ${d[t]} ${a}`})}var m=t(2627);function p(){let e=(e,r)=>{let t=`shape-${r}`,a=0===r?"absolute top-4 left-4":"absolute bottom-4 right-4";switch(e.type){case"circle":return s.jsx(l.Z,{size:e.size,color:e.color,className:a},t);case"rectangle":return s.jsx(o.ZP,{width:e.width,height:e.height,color:e.color,className:a},t);case"triangle":return s.jsx(i.Z,{size:e.size,color:e.color,direction:e.direction,className:a},t);case"halfcircle":return s.jsx(b,{size:e.size,color:e.color,direction:e.direction,className:a},t);default:return null}};return(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("section",{className:"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden",children:[s.jsx("div",{className:"max-w-4xl mx-auto text-center relative z-10",children:s.jsx("h1",{className:"text-display font-bold mb-8",children:"Projects That Speak for Themselves"})}),s.jsx("div",{className:"absolute inset-0 opacity-30",children:s.jsx(m.ZP,{variant:"minimal",className:"w-full h-full"})})]}),s.jsx("section",{className:"px-6 md:px-12 lg:px-24 pb-16 md:pb-24",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{name:"X Platform Redesign",summary:"Speed-focused UI for a growing SaaS",timeline:"3 weeks",outcome:"2x conversions",tech:"React + Tailwind",color:"red",shapes:[{type:"circle",size:"lg",color:"white"},{type:"rectangle",width:"md",height:"sm",color:"black"}]},{name:"Fintech Mobile App",summary:"Clean interface for complex financial data",timeline:"6 weeks",outcome:"40% faster tasks",tech:"React Native + TypeScript",color:"blue",shapes:[{type:"triangle",size:"lg",color:"white",direction:"up"},{type:"circle",size:"md",color:"yellow"}]},{name:"E-commerce Rebuild",summary:"Modern storefront with zero downtime migration",timeline:"8 weeks",outcome:"3x page speed",tech:"Next.js + Shopify",color:"yellow",shapes:[{type:"halfcircle",size:"lg",color:"black",direction:"top"},{type:"rectangle",width:"sm",height:"lg",color:"red"}]},{name:"Healthcare Dashboard",summary:"Data visualization for medical professionals",timeline:"5 weeks",outcome:"50% time saved",tech:"Vue.js + D3.js",color:"red",shapes:[{type:"circle",size:"xl",color:"blue"},{type:"triangle",size:"md",color:"white",direction:"down"}]},{name:"Startup Landing Page",summary:"High-converting page for Series A fundraising",timeline:"2 weeks",outcome:"$2M raised",tech:"Gatsby + Netlify",color:"blue",shapes:[{type:"rectangle",width:"lg",height:"lg",color:"yellow"},{type:"circle",size:"sm",color:"black"}]},{name:"SaaS Product Redesign",summary:"Complete UX overhaul for B2B platform",timeline:"12 weeks",outcome:"60% less support tickets",tech:"React + Node.js",color:"yellow",shapes:[{type:"halfcircle",size:"xl",color:"red",direction:"bottom"},{type:"triangle",size:"sm",color:"blue",direction:"right"}]}].map((r,t)=>(0,s.jsxs)("div",{className:`relative p-8 border-2 border-bauhaus-black bg-bauhaus-${r.color} min-h-[300px] flex flex-col justify-between overflow-hidden`,children:[r.shapes.map((r,t)=>e(r,t)),(0,s.jsxs)("div",{className:"relative z-10",children:[s.jsx("h3",{className:"text-heading font-bold mb-3 text-bauhaus-black",children:r.name}),s.jsx("p",{className:"text-body mb-6 text-bauhaus-black",children:r.summary})]}),(0,s.jsxs)("div",{className:"relative z-10 space-y-2",children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-bauhaus-black",children:[s.jsx("span",{className:"opacity-75",children:"Timeline:"})," ",r.timeline]}),(0,s.jsxs)("div",{className:"text-sm font-medium text-bauhaus-black",children:[s.jsx("span",{className:"opacity-75",children:"Outcome:"})," ",r.outcome]}),(0,s.jsxs)("div",{className:"text-sm font-medium text-bauhaus-black",children:[s.jsx("span",{className:"opacity-75",children:"Tech:"})," ",r.tech]})]})]},t))})})})]})}},1843:(e,r,t)=>{"use strict";t.d(r,{H:()=>i,Z:()=>o});var s=t(5036);let a={sm:"w-8 h-8",md:"w-16 h-16",lg:"w-24 h-24",xl:"w-32 h-32"},l={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function o({size:e="md",color:r="red",className:t=""}){return s.jsx("div",{className:`rounded-full ${a[e]} ${l[r]} ${t}`})}function i({size:e="md",color:r="red",className:t=""}){return s.jsx("div",{className:`rounded-full ${a[e]} ${l[r]} ${t}`,style:{filter:"blur(0.5px)"}})}},3956:(e,r,t)=>{"use strict";t.d(r,{ZP:()=>i,c9:()=>n});var s=t(5036);let a={sm:"w-12",md:"w-24",lg:"w-32",xl:"w-48"},l={sm:"h-8",md:"h-16",lg:"h-24",xl:"h-32"},o={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function i({width:e="md",height:r="md",color:t="blue",className:i=""}){return s.jsx("div",{className:`${a[e]} ${l[r]} ${o[t]} ${i}`})}function n({width:e="md",height:r="md",color:t="blue",className:i=""}){return s.jsx("div",{className:`rounded-3xl ${a[e]} ${l[r]} ${o[t]} ${i}`})}},6193:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(5036);let a={sm:"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]",md:"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]",lg:"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]",xl:"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]"},l=(e,r,t)=>{let s=a[e];switch(t){case"up":default:return`${s} border-l-transparent border-r-transparent`;case"down":return s.replace("border-b-","border-t-")+" border-l-transparent border-r-transparent";case"left":return s.replace("border-l-","border-r-").replace("border-r-","border-t-").replace("border-b-","border-l-")+" border-t-transparent border-b-transparent";case"right":return s.replace("border-r-","border-l-").replace("border-l-","border-t-").replace("border-b-","border-r-")+" border-t-transparent border-b-transparent"}};function o({size:e="md",color:r="yellow",direction:t="up",className:a=""}){let o=l(e,r,t);return s.jsx("div",{className:`${o} ${a}`,style:{borderBottomColor:"up"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderTopColor:"down"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderLeftColor:"right"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderRightColor:"left"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent"}})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[847,515,904,462,913],()=>t(5335));module.exports=s})();