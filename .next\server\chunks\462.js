exports.id=462,exports.ids=[462],exports.modules={4862:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,6840,23)),Promise.resolve().then(n.t.bind(n,8771,23)),Promise.resolve().then(n.t.bind(n,3225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,3982,23))},172:(e,t,n)=>{Promise.resolve().then(n.bind(n,2184)),Promise.resolve().then(n.bind(n,7022)),Promise.resolve().then(n.bind(n,3532))},2184:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a}),n(2295);var r=n(3729);function a(){return(0,r.useEffect)(()=>{let e=()=>{if("hidden"===document.visibilityState){"AbortController"in window&&(window.__abortControllers||[]).forEach(e=>{e.abort()});let e=setTimeout(()=>{},0);for(let t=0;t<e;t++)clearTimeout(t);let t=setInterval(()=>{},0);for(let e=0;e<t;e++)clearInterval(e);"WebSocket"in window&&(window.__webSockets||[]).forEach(e=>{(e.readyState===WebSocket.OPEN||e.readyState===WebSocket.CONNECTING)&&e.close()})}},t=()=>{"navigator"in window&&navigator},n=e=>{e.persisted},r=e=>{e.persisted&&document.querySelectorAll("[data-time-sensitive]").forEach(e=>{let t=new CustomEvent("bfcache-refresh");e.dispatchEvent(t)})};return document.addEventListener("visibilitychange",e),window.addEventListener("beforeunload",t),window.addEventListener("pagehide",n),window.addEventListener("pageshow",r),"performance"in window&&"getEntriesByType"in performance&&performance.getEntriesByType("navigation").forEach(e=>{}),()=>{document.removeEventListener("visibilitychange",e),window.removeEventListener("beforeunload",t),window.removeEventListener("pagehide",n),window.removeEventListener("pageshow",r)}},[]),null}},7022:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(3729);function a(){return(0,r.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll(".defer-load"),t=new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&(e.target.classList.add("loaded"),t.unobserve(e.target))})},{rootMargin:"50px"});e.forEach(e=>t.observe(e));let n=(e,t="all")=>{let n=document.createElement("link");return n.rel="stylesheet",n.href=e,n.media=t,"all"!==t&&(n.onload=function(){this.media="all"}),document.head.appendChild(n),n};["/styles/non-critical.css"].forEach(e=>{document.querySelector(`link[href="${e}"]`)||n(e,"print")}),["/styles/critical.css"].forEach(e=>{let t=document.createElement("link");t.rel="preload",t.as="style",t.href=e,document.head.appendChild(t)})};return"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):"requestIdleCallback"in window?requestIdleCallback(e):setTimeout(e,100),setTimeout(()=>{Array.from(document.styleSheets).forEach(e=>{try{Array.from(e.cssRules||e.rules||[]).forEach(e=>{e.type!==CSSRule.FONT_FACE_RULE||e.style.fontDisplay||(e.style.fontDisplay="swap")})}catch(e){}})},100),()=>{document.removeEventListener("DOMContentLoaded",e)}},[]),null}},3532:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(3729);function a(){return(0,r.useEffect)(()=>{let e={lcp:0,fid:0,cls:0,fcp:0,ttfb:0},t=new PerformanceObserver(t=>{let n=t.getEntries(),r=n[n.length-1];r&&(e.lcp=Math.round(r.startTime))}),n=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{e.fid=Math.round(t.processingStart-t.startTime)})}),r=new PerformanceObserver(t=>{let n=0;for(let e of t.getEntries())e.hadRecentInput||(n+=e.value);n>0&&(e.cls=n)}),a=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{e.fcp=Math.round(t.startTime)})}),o=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{e.ttfb=Math.round(t.responseStart-t.requestStart)})});try{t.observe({entryTypes:["largest-contentful-paint"]})}catch(e){}try{n.observe({entryTypes:["first-input"]})}catch(e){}try{r.observe({entryTypes:["layout-shift"]})}catch(e){}try{a.observe({entryTypes:["first-contentful-paint"]})}catch(e){}try{o.observe({entryTypes:["navigation"]})}catch(e){}let i=()=>{"navigator"in window&&"sendBeacon"in navigator&&JSON.stringify({url:window.location.href,vitals:e,timestamp:Date.now(),userAgent:navigator.userAgent})},s=()=>{"hidden"===document.visibilityState&&i()};return document.addEventListener("visibilitychange",s),()=>{t.disconnect(),n.disconnect(),r.disconnect(),a.disconnect(),o.disconnect(),document.removeEventListener("visibilitychange",s)}},[]),null}},488:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>E,metadata:()=>k});var r=n(5036),a=n(3783),o=n.n(a);n(5023);var i=n(6843);let s=(0,i.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\performance\PerformanceMonitor.tsx`),{__esModule:c,$$typeof:l}=s,d=s.default;var f=n(3638),m=n.n(f);function p(){return r.jsx(m(),{id:"443b0c86ff73f6b3",children:'html{font-family:var(--font-barlow),-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;font-display:swap}body{margin:0;padding:0;color:#000;background-color:#f0ebde;font-family:var(--font-barlow),-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;line-height:1.6}.text-hero{font-size:4rem;line-height:1.1;letter-spacing:-.02em;font-weight:700;font-family:var(--font-barlow),sans-serif;contain:layout style paint;margin:0;padding:0}.nav-critical{position:fixed;top:0;left:0;right:0;z-index:50;background-color:#f0ebde;border-bottom:1px solid rgba(0,0,0,.1);height:80px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;padding:0 2rem}@media(max-width:768px){.text-hero{font-size:2.5rem}.nav-critical{padding:0 1rem}}@media(max-width:640px){.text-hero{font-size:2rem}}.hero-container{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;padding-top:80px}.btn-primary{display:inline-block;padding:.875rem 1.5rem;border:2px solid#000;background-color:transparent;color:#000;font-weight:700;text-transform:uppercase;letter-spacing:.05em;text-decoration:none;-webkit-border-radius:1rem;-moz-border-radius:1rem;border-radius:1rem;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease;cursor:pointer}.btn-primary:hover{background-color:#000;color:#f0ebde}.loading-placeholder{opacity:0;-webkit-animation:fadeIn.3s ease-in-out forwards;-moz-animation:fadeIn.3s ease-in-out forwards;-o-animation:fadeIn.3s ease-in-out forwards;animation:fadeIn.3s ease-in-out forwards}@-webkit-keyframes fadeIn{to{opacity:1}}@-moz-keyframes fadeIn{to{opacity:1}}@-o-keyframes fadeIn{to{opacity:1}}@keyframes fadeIn{to{opacity:1}}.container{max-width:80rem;margin:0 auto;padding:0 1rem}.flex{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.items-center{-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.justify-center{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.justify-between{-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.text-center{text-align:center}.font-bold{font-weight:700}.uppercase{text-transform:uppercase}.tracking-wide{letter-spacing:.05em}.defer-load{opacity:0;-webkit-transform:translatey(20px);-moz-transform:translatey(20px);-ms-transform:translatey(20px);-o-transform:translatey(20px);transform:translatey(20px);-webkit-transition:opacity.3s ease,-webkit-transform.3s ease;-moz-transition:opacity.3s ease,-moz-transform.3s ease;-o-transition:opacity.3s ease,-o-transform.3s ease;transition:opacity.3s ease,-webkit-transform.3s ease;transition:opacity.3s ease,-moz-transform.3s ease;transition:opacity.3s ease,-o-transform.3s ease;transition:opacity.3s ease,transform.3s ease}.defer-load.loaded{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-ms-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}'})}let b=(0,i.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\performance\CSSOptimizer.tsx`),{__esModule:u,$$typeof:y}=b,h=b.default,g=(0,i.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\performance\BFCacheOptimizer.tsx`),{__esModule:w,$$typeof:x}=g,v=g.default,k={title:"navhaus | what matters, made real",description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.",icons:{icon:"/images/icon.png",shortcut:"/images/icon.png",apple:"/images/icon.png"}};function E({children:e}){return(0,r.jsxs)("html",{lang:"en",className:o().variable,children:[(0,r.jsxs)("head",{children:[r.jsx(p,{}),r.jsx("link",{rel:"dns-prefetch",href:"https://fonts.googleapis.com"}),r.jsx("link",{rel:"dns-prefetch",href:"https://fonts.gstatic.com"}),r.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),r.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,r.jsxs)("body",{className:o().className,children:[r.jsx(h,{}),r.jsx(v,{}),r.jsx(d,{}),e]})]})}},5023:()=>{}};