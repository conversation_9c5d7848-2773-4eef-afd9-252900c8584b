import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // Get the pathname
  const pathname = request.nextUrl.pathname

  // Set security headers
  response.headers.set('X-DNS-Prefetch-Control', 'on')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')

  // Optimize caching for bfcache compatibility
  if (process.env.NODE_ENV === 'production') {
    // For static assets, use aggressive caching
    if (pathname.startsWith('/_next/static/') || 
        pathname.startsWith('/images/') ||
        pathname.match(/\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2)$/)) {
      response.headers.set(
        'Cache-Control',
        'public, max-age=31536000, immutable'
      )
    }
    // For HTML pages, use cache but allow revalidation for bfcache
    else if (pathname.endsWith('.html') || !pathname.includes('.')) {
      response.headers.set(
        'Cache-Control',
        'public, max-age=0, must-revalidate'
      )
      // Remove any no-store directives that prevent bfcache
      response.headers.delete('Cache-Control')
      response.headers.set(
        'Cache-Control',
        'public, max-age=3600, stale-while-revalidate=86400'
      )
    }
  } else {
    // In development, use minimal caching to allow hot reloading
    response.headers.set(
      'Cache-Control',
      'no-cache, no-store, must-revalidate'
    )
  }

  // Add performance hints
  response.headers.set('X-Performance-Optimized', 'true')

  // Add timing headers for debugging
  if (process.env.NODE_ENV === 'development') {
    response.headers.set('X-Timestamp', Date.now().toString())
  }

  return response
}

// Configure which paths the middleware runs on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
