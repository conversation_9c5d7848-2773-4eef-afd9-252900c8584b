"use strict";exports.id=913,exports.ids=[913],exports.modules={6454:(e,r,l)=>{l.r(r),l.d(r,{FeatureComposition:()=>u,HeroComposition:()=>c,MinimalComposition:()=>h,StaticHeroComposition:()=>d,default:()=>m});var t=l(2295),s=l(6991),a=l(2547),n=l(5084),o=l(1562),i=l(3729);function d({className:e=""}){return(0,t.jsxs)("div",{className:`relative ${e}`,children:[t.jsx(s.Xg,{className:"text-black h-full",opacity:"hero"}),t.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 opacity-80",children:t.jsx(s.B$,{color:"blue",direction:"right",className:"w-full h-full"})}),t.jsx("div",{className:"absolute bottom-0 left-0 w-48 h-48",children:t.jsx(s.kw,{color:"yellow",corner:"bottom-left",className:"w-full h-full"})}),t.jsx("div",{className:"absolute top-1/4 left-1/3 w-24 h-24 opacity-90",children:t.jsx(s.t6,{color:"red",className:"w-full h-full"})}),t.jsx("div",{className:"absolute top-1/2 right-1/4 w-20 h-16",children:t.jsx(a.H,{size:"lg",color:"yellow",className:"h-full"})}),t.jsx("div",{className:"absolute bottom-1/3 left-1/4 w-16 h-6",children:t.jsx(s.DR,{color:"black",className:"w-full h-full"})}),t.jsx("div",{className:"absolute top-3/4 right-1/3 w-12 h-12",children:t.jsx(s.uM,{color:"blue",className:"w-full h-full"})})]})}function c({className:e=""}){let[r,l]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{let e=setTimeout(()=>{l(!0)},100);return()=>clearTimeout(e)},[]),r)?(0,t.jsxs)("div",{className:`relative ${e}`,children:[t.jsx(o.AnimatedSoftGrid,{className:"text-black h-full",opacity:"hero",animationPreset:"subtle",animationIndex:0}),t.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 opacity-80",children:t.jsx(o.AnimatedHalfCircle,{color:"blue",direction:"right",className:"w-full h-full",animationPreset:"gentle",animationIndex:1})}),t.jsx("div",{className:"absolute bottom-0 left-0 w-48 h-48",children:t.jsx(o.AnimatedQuarterCircle,{color:"yellow",corner:"bottom-left",className:"w-full h-full",animationPreset:"flowing",animationIndex:2})}),t.jsx("div",{className:"absolute top-1/4 left-1/3 w-24 h-24 opacity-90",children:t.jsx(o.AnimatedBlob,{color:"red",className:"w-full h-full",animationPreset:"dynamic",animationIndex:3})}),t.jsx("div",{className:"absolute top-1/2 right-1/4 w-20 h-16",children:t.jsx(o.AnimatedSoftCircle,{size:"lg",color:"yellow",className:"h-full",animationPreset:"energetic",animationIndex:4})}),t.jsx("div",{className:"absolute bottom-1/3 left-1/4 w-16 h-6",children:t.jsx(o.AnimatedPill,{color:"black",className:"w-full h-full",animationPreset:"horizontal",animationIndex:5})}),t.jsx("div",{className:"absolute top-3/4 right-1/3 w-12 h-12",children:t.jsx(o.AnimatedRoundedRect,{color:"blue",className:"w-full h-full",animationPreset:"pulse",animationIndex:6})})]}):t.jsx(d,{className:e})}function u({className:e=""}){return(0,t.jsxs)("div",{className:`relative ${e}`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-8",children:[t.jsx(a.H,{size:"xl",color:"blue"}),(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[t.jsx(n.c9,{width:"lg",height:"xl",color:"yellow"}),t.jsx(a.H,{size:"lg",color:"black"})]}),t.jsx(s.kw,{color:"red",corner:"top-right",className:"w-32 h-32"})]}),t.jsx(s.B$,{color:"blue",direction:"left",className:"absolute -top-8 -right-8 w-24 h-24 opacity-60"})]})}function h({className:e=""}){return(0,t.jsxs)("div",{className:`relative ${e}`,children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-8 items-center",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[t.jsx(a.H,{size:"md",color:"black"}),t.jsx(s.DR,{color:"yellow",className:"w-20 h-8"})]}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(s.uM,{color:"red",className:"w-24 h-24"}),t.jsx(a.H,{size:"sm",color:"black",className:"absolute -bottom-2 -right-2"})]}),t.jsx(s.t6,{color:"blue",className:"w-20 h-20"})]}),t.jsx("svg",{className:"absolute inset-0 w-full h-full pointer-events-none opacity-20",children:t.jsx("path",{d:"M 50 50 Q 150 100 250 50",stroke:"currentColor",strokeWidth:"1",fill:"none",className:"text-black"})})]})}function m({variant:e="hero",className:r=""}){switch(e){case"hero":default:return t.jsx(c,{className:r});case"feature":return t.jsx(u,{className:r});case"minimal":return t.jsx(h,{className:r})}}},9705:(e,r,l)=>{l.r(r),l.d(r,{default:()=>c});var t=l(2295),s=l(3729),a=l(783),n=l.n(a),o=l(1223),i=l.n(o),d=l(2254);function c(){let[e,r]=(0,s.useState)(!1),l=(0,d.usePathname)(),a=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/work",label:"Work"},{href:"/contact",label:"Contact"}];return t.jsx("header",{className:"w-full py-6 px-6 md:px-12 lg:px-24",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("nav",{className:"flex justify-between items-center",children:[t.jsx(n(),{href:"/",className:"flex items-center",children:t.jsx(i(),{src:"/images/logo.png",alt:"Navhaus",width:120,height:40,className:"h-8 w-auto -mt-[15px]"})}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[a.map(e=>t.jsx(n(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${l===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,children:e.label},e.href)),t.jsx(n(),{href:"/contact",className:"btn-primary ml-8",children:"Start Project"})]}),t.jsx("button",{className:"md:hidden text-bauhaus-black",onClick:()=>r(!e),children:t.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e?t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&t.jsx("div",{className:"md:hidden mt-6 py-6 border-t border-bauhaus-black",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[a.map(e=>t.jsx(n(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${l===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,onClick:()=>r(!1),children:e.label},e.href)),t.jsx(n(),{href:"/contact",className:"btn-primary inline-block mt-4",onClick:()=>r(!1),children:"Start Project"})]})})]})})}},1562:(e,r,l)=>{l.r(r),l.d(r,{AnimatedBlob:()=>N,AnimatedCircle:()=>m,AnimatedHalfCircle:()=>g,AnimatedPill:()=>y,AnimatedQuarterCircle:()=>v,AnimatedRectangle:()=>f,AnimatedRoundedRect:()=>w,AnimatedRoundedRectangle:()=>x,AnimatedSoftCircle:()=>b,AnimatedSoftGrid:()=>j,AnimatedTriangle:()=>p});var t=l(2295),s=l(3729);function a(e){let[r,l]=(0,s.useState)(0),t=(0,s.useRef)(null),[a,n]=(0,s.useState)({x:0,y:0}),[o,i]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let r=setTimeout(()=>{let r=e.randomSeed||Math.random(),l=43758.5453*Math.sin(45.164*r)%1*Math.PI*2,t=.8+.4*Math.abs(43758.5453*Math.sin(12.9898*r)%1);n({x:Math.cos(l)*t,y:Math.sin(l)*t}),i(!0)},50);return()=>clearTimeout(r)},[e.randomSeed]),(0,s.useEffect)(()=>{if(!o)return;let e=()=>{l(window.scrollY)};return window.addEventListener("scroll",e,{passive:!0}),()=>{window.removeEventListener("scroll",e)}},[o]),{ref:t,style:{transform:(()=>{var l;if(!t.current||!o)return"translate3d(0, 0, 0)";let s=t.current.getBoundingClientRect(),n=s.top+r,i=(l=Math.max(0,Math.min(1,(r-n+window.innerHeight)/(window.innerHeight+s.height))))<.5?2*l*l:-1+(4-2*l)*l,d=0,c=0;return("x"===e.direction||"both"===e.direction)&&(d=i*e.intensity*a.x*e.speed),("y"===e.direction||"both"===e.direction)&&(c=i*e.intensity*a.y*e.speed),`translate3d(${d}px, ${c}px, 0)`})(),transition:"transform 0.1s ease-out",willChange:"transform"}}}let n={subtle:{direction:"both",intensity:25,speed:.6},gentle:{direction:"both",intensity:35,speed:.8},dynamic:{direction:"both",intensity:55,speed:1.2},flowing:{direction:"both",intensity:40,speed:.9},energetic:{direction:"both",intensity:60,speed:1.4},drift:{direction:"both",intensity:30,speed:.5},pulse:{direction:"both",intensity:45,speed:1.1},float:{direction:"both",intensity:20,speed:.4},horizontal:{direction:"x",intensity:40,speed:.8}};var o=l(2547),i=l(5084),d=l(2678),c=l(9668),u=l(6991);let h=(e,r=0)=>{let l=0,t=`${e}-${r}`;for(let e=0;e<t.length;e++)l=(l<<5)-l+t.charCodeAt(e),l&=l;return Math.abs(l)/2147483647};function m({animationPreset:e="gentle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("circle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(o.Z,{...l})})}function b({animationPreset:e="subtle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("soft-circle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(o.H,{...l})})}function f({animationPreset:e="gentle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("rectangle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(i.ZP,{...l})})}function x({animationPreset:e="gentle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("rounded-rectangle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(i.c9,{...l})})}function p({animationPreset:e="dynamic",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("triangle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(d.Z,{...l})})}function g({animationPreset:e="gentle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("half-circle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(c.Z,{...l})})}function j({animationPreset:e="subtle",animationIndex:r=0,opacity:l="default",...s}){let o=a({...n[e],randomSeed:h("soft-grid",r)});return t.jsx("div",{ref:o.ref,className:"h-full",style:o.style,children:t.jsx(u.Xg,{opacity:l,...s})})}function w({animationPreset:e="gentle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("rounded-rect",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(u.uM,{...l})})}function y({animationPreset:e="horizontal",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("pill",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(u.DR,{...l})})}function N({animationPreset:e="dynamic",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("blob",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(u.t6,{...l})})}function v({animationPreset:e="gentle",animationIndex:r=0,...l}){let s=a({...n[e],randomSeed:h("quarter-circle",r)});return t.jsx("div",{ref:s.ref,className:"h-full",style:s.style,children:t.jsx(u.kw,{...l})})}},2547:(e,r,l)=>{l.d(r,{H:()=>o,Z:()=>n});var t=l(2295);let s={sm:"w-8 h-8",md:"w-16 h-16",lg:"w-24 h-24",xl:"w-32 h-32"},a={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function n({size:e="md",color:r="red",className:l=""}){return t.jsx("div",{className:`rounded-full ${s[e]} ${a[r]} ${l}`})}function o({size:e="md",color:r="red",className:l=""}){return t.jsx("div",{className:`rounded-full ${s[e]} ${a[r]} ${l}`,style:{filter:"blur(0.5px)"}})}},9668:(e,r,l)=>{l.d(r,{Z:()=>i});var t=l(2295);let s={sm:"w-8 h-4",md:"w-16 h-8",lg:"w-24 h-12",xl:"w-32 h-16"},a={red:"bg-bauhaus-red",yellow:"bg-bauhaus-yellow",blue:"bg-bauhaus-blue",black:"bg-bauhaus-black",white:"bg-bauhaus-white border border-bauhaus-black"},n={top:"rounded-t-full",bottom:"rounded-b-full",left:"rounded-l-full",right:"rounded-r-full"},o=e=>{switch(e){case"left":case"right":return"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32";default:return""}};function i({size:e="md",color:r="red",direction:l="top",className:i=""}){let d=o(l)||s[e];return t.jsx("div",{className:`${d} ${a[r]} ${n[l]} ${i}`})}},5084:(e,r,l)=>{l.d(r,{ZP:()=>o,c9:()=>i});var t=l(2295);let s={sm:"w-12",md:"w-24",lg:"w-32",xl:"w-48"},a={sm:"h-8",md:"h-16",lg:"h-24",xl:"h-32"},n={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function o({width:e="md",height:r="md",color:l="blue",className:o=""}){return t.jsx("div",{className:`${s[e]} ${a[r]} ${n[l]} ${o}`})}function i({width:e="md",height:r="md",color:l="blue",className:o=""}){return t.jsx("div",{className:`rounded-3xl ${s[e]} ${a[r]} ${n[l]} ${o}`})}},6991:(e,r,l)=>{l.d(r,{B$:()=>o,DR:()=>a,Xg:()=>d,kw:()=>i,t6:()=>n,uM:()=>s});var t=l(2295);function s({className:e="",color:r="blue"}){return t.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} rounded-3xl ${e}`})}function a({className:e="",color:r="yellow"}){return t.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} rounded-full ${e}`})}function n({className:e="",color:r="red"}){return t.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} ${e}`,style:{borderRadius:"60% 40% 30% 70% / 60% 30% 70% 40%"}})}function o({className:e="",color:r="blue",direction:l="right"}){return t.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} ${{right:"rounded-l-full",left:"rounded-r-full",top:"rounded-b-full",bottom:"rounded-t-full"}[l]} ${e}`})}function i({className:e="",color:r="yellow",corner:l="top-left"}){return t.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} ${{"top-left":"rounded-br-full","top-right":"rounded-bl-full","bottom-left":"rounded-tr-full","bottom-right":"rounded-tl-full"}[l]} ${e}`})}function d({className:e="",opacity:r="default"}){return t.jsx("div",{className:`absolute inset-0 ${"hero"===r?"opacity-40":"opacity-20"} ${e}`,children:(0,t.jsxs)("svg",{width:"100%",height:"100%",className:"w-full h-full",children:[t.jsx("defs",{children:t.jsx("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:t.jsx("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"hero"===r?"0.5":"0.3"})})}),t.jsx("rect",{width:"100%",height:"100%",fill:"url(#grid)"})]})})}},2678:(e,r,l)=>{l.d(r,{Z:()=>n});var t=l(2295);let s={sm:"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]",md:"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]",lg:"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]",xl:"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]"},a=(e,r,l)=>{let t=s[e];switch(l){case"up":default:return`${t} border-l-transparent border-r-transparent`;case"down":return t.replace("border-b-","border-t-")+" border-l-transparent border-r-transparent";case"left":return t.replace("border-l-","border-r-").replace("border-r-","border-t-").replace("border-b-","border-l-")+" border-t-transparent border-b-transparent";case"right":return t.replace("border-r-","border-l-").replace("border-l-","border-t-").replace("border-b-","border-r-")+" border-t-transparent border-b-transparent"}};function n({size:e="md",color:r="yellow",direction:l="up",className:s=""}){let n=a(e,r,l);return t.jsx("div",{className:`${n} ${s}`,style:{borderBottomColor:"up"===l?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderTopColor:"down"===l?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderLeftColor:"right"===l?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderRightColor:"left"===l?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent"}})}},2627:(e,r,l)=>{l.d(r,{ZP:()=>i});var t=l(6843);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\compositions\OrganicComposition.tsx`),{__esModule:a,$$typeof:n}=s,o=s.default;(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\compositions\OrganicComposition.tsx#StaticHeroComposition`),(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\compositions\OrganicComposition.tsx#HeroComposition`),(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\compositions\OrganicComposition.tsx#FeatureComposition`),(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\compositions\OrganicComposition.tsx#MinimalComposition`);let i=o},7621:(e,r,l)=>{l.d(r,{Z:()=>u});var t=l(5036);let s=(0,l(6843).createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\layout\Header.tsx`),{__esModule:a,$$typeof:n}=s,o=s.default;var i=l(5904),d=l.n(i);function c(){return(0,t.jsxs)("footer",{className:"w-full py-12 px-6 md:px-12 lg:px-24 border-t border-bauhaus-black",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center space-y-6 md:space-y-0",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{className:"mb-2",children:t.jsx(d(),{src:"/images/logo.png",alt:"Navhaus",width:100,height:32,className:"h-6 w-auto"})}),t.jsx("p",{className:"text-sm text-gray-600",children:"What matters, made real."})]}),(0,t.jsxs)("div",{className:"text-right",children:[t.jsx("p",{className:"text-sm font-medium mb-1",children:"Ready to build something?"}),t.jsx("a",{href:"mailto:<EMAIL>",className:"text-sm text-bauhaus-red hover:underline",children:"<EMAIL>"})]})]}),t.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200 text-center",children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["\xa9 ",new Date().getFullYear()," Navhaus. All rights reserved."]})})]})}function u({children:e,className:r=""}){return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[t.jsx(o,{}),t.jsx("main",{className:`flex-1 ${r}`,children:e}),t.jsx(c,{})]})}}};