(()=>{var e={};e.id=327,e.ids=[327],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},67:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>o.a,__next_app__:()=>b,originalPathname:()=>u,pages:()=>i,routeModule:()=>h,tree:()=>c});var t=a(482),s=a(9108),l=a(2563),o=a.n(l),n=a(8300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(r,d);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1215)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,488)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9361,23)),"next/dist/client/components/not-found-error"]}],i=["C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx"],u="/contact/page",b={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5951:(e,r,a)=>{Promise.resolve().then(a.bind(a,2955))},2955:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var t=a(2295),s=a(3729),l=a(9705),o=a(1223),n=a.n(o);function d(){return(0,t.jsxs)("footer",{className:"w-full py-12 px-6 md:px-12 lg:px-24 border-t border-bauhaus-black",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center space-y-6 md:space-y-0",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{className:"mb-2",children:t.jsx(n(),{src:"/images/logo.png",alt:"Navhaus",width:100,height:32,className:"h-6 w-auto"})}),t.jsx("p",{className:"text-sm text-gray-600",children:"What matters, made real."})]}),(0,t.jsxs)("div",{className:"text-right",children:[t.jsx("p",{className:"text-sm font-medium mb-1",children:"Ready to build something?"}),t.jsx("a",{href:"mailto:<EMAIL>",className:"text-sm text-bauhaus-red hover:underline",children:"<EMAIL>"})]})]}),t.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200 text-center",children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["\xa9 ",new Date().getFullYear()," Navhaus. All rights reserved."]})})]})}function c({children:e,className:r=""}){return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[t.jsx(l.default,{}),t.jsx("main",{className:`flex-1 ${r}`,children:e}),t.jsx(d,{})]})}var i=a(2547),u=a(5084),b=a(2678),h=a(9668);function m(){let[e,r]=(0,s.useState)({name:"",email:"",message:""}),a=a=>{r({...e,[a.target.name]:a.target.value})};return t.jsx(c,{children:t.jsx("section",{className:"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden",children:t.jsx("div",{className:"max-w-6xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-start",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-display font-bold mb-8",children:"Have something worth building? Let's talk."}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r({name:"",email:"",message:""})},className:"space-y-8",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"name",className:"block text-sm font-bold uppercase tracking-wide mb-3",children:"Name"}),t.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:a,required:!0,className:"w-full px-4 py-4 border-2 border-bauhaus-black bg-brand-background text-bauhaus-black focus:outline-none focus:border-bauhaus-red transition-colors duration-200"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-bold uppercase tracking-wide mb-3",children:"Email"}),t.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:a,required:!0,className:"w-full px-4 py-4 border-2 border-bauhaus-black bg-brand-background text-bauhaus-black focus:outline-none focus:border-bauhaus-red transition-colors duration-200"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"message",className:"block text-sm font-bold uppercase tracking-wide mb-3",children:"Message"}),t.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:a,required:!0,rows:6,className:"w-full px-4 py-4 border-2 border-bauhaus-black bg-brand-background text-bauhaus-black focus:outline-none focus:border-bauhaus-red transition-colors duration-200 resize-none"})]}),t.jsx("button",{type:"submit",className:"btn-red",children:"Send It"})]})]}),(0,t.jsxs)("div",{className:"relative h-96 lg:h-[600px]",children:[t.jsx(i.Z,{size:"xl",color:"red",className:"absolute top-12 right-16 opacity-80"}),t.jsx(u.ZP,{width:"lg",height:"xl",color:"blue",className:"absolute top-32 left-12 rotate-12 opacity-90"}),t.jsx(b.Z,{size:"xl",color:"yellow",direction:"up",className:"absolute bottom-24 right-8 opacity-85"}),t.jsx(h.Z,{size:"lg",color:"black",direction:"left",className:"absolute top-8 left-32 rotate-45"}),t.jsx(i.Z,{size:"lg",color:"yellow",className:"absolute bottom-8 left-8 opacity-70"}),t.jsx(u.ZP,{width:"md",height:"sm",color:"red",className:"absolute top-48 right-32 -rotate-45 opacity-75"}),t.jsx(b.Z,{size:"md",color:"blue",direction:"down",className:"absolute bottom-32 left-24 opacity-80"}),(0,t.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[t.jsx("div",{className:"w-full h-px bg-bauhaus-black absolute top-1/4"}),t.jsx("div",{className:"w-full h-px bg-bauhaus-black absolute top-1/2"}),t.jsx("div",{className:"w-full h-px bg-bauhaus-black absolute top-3/4"}),t.jsx("div",{className:"w-px h-full bg-bauhaus-black absolute left-1/4"}),t.jsx("div",{className:"w-px h-full bg-bauhaus-black absolute left-1/2"}),t.jsx("div",{className:"w-px h-full bg-bauhaus-black absolute left-3/4"})]})]})]})})})})}},9705:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i});var t=a(2295),s=a(3729),l=a(783),o=a.n(l),n=a(1223),d=a.n(n),c=a(2254);function i(){let[e,r]=(0,s.useState)(!1),a=(0,c.usePathname)(),l=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/work",label:"Work"},{href:"/contact",label:"Contact"}];return t.jsx("header",{className:"w-full py-6 px-6 md:px-12 lg:px-24",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("nav",{className:"flex justify-between items-center",children:[t.jsx(o(),{href:"/",className:"flex items-center",children:t.jsx(d(),{src:"/images/logo.png",alt:"Navhaus",width:120,height:40,className:"h-8 w-auto -mt-[15px]"})}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[l.map(e=>t.jsx(o(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${a===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,children:e.label},e.href)),t.jsx(o(),{href:"/contact",className:"btn-primary ml-8",children:"Start Project"})]}),t.jsx("button",{className:"md:hidden text-bauhaus-black",onClick:()=>r(!e),children:t.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e?t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&t.jsx("div",{className:"md:hidden mt-6 py-6 border-t border-bauhaus-black",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[l.map(e=>t.jsx(o(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${a===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,onClick:()=>r(!1),children:e.label},e.href)),t.jsx(o(),{href:"/contact",className:"btn-primary inline-block mt-4",onClick:()=>r(!1),children:"Start Project"})]})})]})})}},2547:(e,r,a)=>{"use strict";a.d(r,{H:()=>n,Z:()=>o});var t=a(2295);let s={sm:"w-8 h-8",md:"w-16 h-16",lg:"w-24 h-24",xl:"w-32 h-32"},l={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function o({size:e="md",color:r="red",className:a=""}){return t.jsx("div",{className:`rounded-full ${s[e]} ${l[r]} ${a}`})}function n({size:e="md",color:r="red",className:a=""}){return t.jsx("div",{className:`rounded-full ${s[e]} ${l[r]} ${a}`,style:{filter:"blur(0.5px)"}})}},9668:(e,r,a)=>{"use strict";a.d(r,{Z:()=>d});var t=a(2295);let s={sm:"w-8 h-4",md:"w-16 h-8",lg:"w-24 h-12",xl:"w-32 h-16"},l={red:"bg-bauhaus-red",yellow:"bg-bauhaus-yellow",blue:"bg-bauhaus-blue",black:"bg-bauhaus-black",white:"bg-bauhaus-white border border-bauhaus-black"},o={top:"rounded-t-full",bottom:"rounded-b-full",left:"rounded-l-full",right:"rounded-r-full"},n=e=>{switch(e){case"left":case"right":return"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32";default:return""}};function d({size:e="md",color:r="red",direction:a="top",className:d=""}){let c=n(a)||s[e];return t.jsx("div",{className:`${c} ${l[r]} ${o[a]} ${d}`})}},5084:(e,r,a)=>{"use strict";a.d(r,{ZP:()=>n,c9:()=>d});var t=a(2295);let s={sm:"w-12",md:"w-24",lg:"w-32",xl:"w-48"},l={sm:"h-8",md:"h-16",lg:"h-24",xl:"h-32"},o={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function n({width:e="md",height:r="md",color:a="blue",className:n=""}){return t.jsx("div",{className:`${s[e]} ${l[r]} ${o[a]} ${n}`})}function d({width:e="md",height:r="md",color:a="blue",className:n=""}){return t.jsx("div",{className:`rounded-3xl ${s[e]} ${l[r]} ${o[a]} ${n}`})}},2678:(e,r,a)=>{"use strict";a.d(r,{Z:()=>o});var t=a(2295);let s={sm:"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]",md:"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]",lg:"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]",xl:"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]"},l=(e,r,a)=>{let t=s[e];switch(a){case"up":default:return`${t} border-l-transparent border-r-transparent`;case"down":return t.replace("border-b-","border-t-")+" border-l-transparent border-r-transparent";case"left":return t.replace("border-l-","border-r-").replace("border-r-","border-t-").replace("border-b-","border-l-")+" border-t-transparent border-b-transparent";case"right":return t.replace("border-r-","border-l-").replace("border-l-","border-t-").replace("border-b-","border-r-")+" border-t-transparent border-b-transparent"}};function o({size:e="md",color:r="yellow",direction:a="up",className:s=""}){let o=l(e,r,a);return t.jsx("div",{className:`${o} ${s}`,style:{borderBottomColor:"up"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderTopColor:"down"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderLeftColor:"right"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderRightColor:"left"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent"}})}},1215:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>l,__esModule:()=>s,default:()=>o});let t=(0,a(6843).createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\app\contact\page.tsx`),{__esModule:s,$$typeof:l}=t,o=t.default}};var r=require("../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[847,515,462],()=>a(67));module.exports=t})();