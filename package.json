{"name": "navhaus-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && node scripts/optimize-build.js", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build && node scripts/optimize-build.js", "start": "next start", "lint": "next lint", "lighthouse": "node scripts/measure-lcp.js", "optimize": "node scripts/optimize-build.js"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}