import type { Metada<PERSON> } from 'next'
import { <PERSON> } from 'next/font/google'
import './globals.css'
import PerformanceMonitor from '@/components/performance/PerformanceMonitor'
import CriticalCSS from '@/components/performance/CriticalCSS'
import CSSOptimizer from '@/components/performance/CSSOptimizer'
import BFCacheOptimizer from '@/components/performance/BFCacheOptimizer'

// Optimize font loading with Next.js font optimization
const barlow = Barlow({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'], // Reduce font weights to decrease bundle size
  display: 'swap', // Use font-display: swap for better LCP
  preload: true,
  variable: '--font-barlow',
  fallback: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
})

export const metadata: Metadata = {
  title: 'navhaus | what matters, made real',
  description: 'Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.',
  icons: {
    icon: '/images/icon.png',
    shortcut: '/images/icon.png',
    apple: '/images/icon.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={barlow.variable}>
      <head>
        {/* Critical CSS inlined for faster rendering */}
        <CriticalCSS />
        {/* DNS prefetch for any external resources */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
        {/* Preconnect to font origins for faster font loading */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={barlow.className}>
        <CSSOptimizer />
        <BFCacheOptimizer />
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  )
}
