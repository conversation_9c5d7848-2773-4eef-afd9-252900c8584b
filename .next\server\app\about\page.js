(()=>{var e={};e.id=301,e.ids=[301],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},7915:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>b,pages:()=>c,routeModule:()=>x,tree:()=>i});var s=t(482),a=t(9108),l=t(2563),o=t.n(l),d=t(8300),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(r,n);let i=["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3271)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,488)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx"],b="/about/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},5073:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,1900,23)),Promise.resolve().then(t.bind(t,9705))},9705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(2295),a=t(3729),l=t(783),o=t.n(l),d=t(1223),n=t.n(d),i=t(2254);function c(){let[e,r]=(0,a.useState)(!1),t=(0,i.usePathname)(),l=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/work",label:"Work"},{href:"/contact",label:"Contact"}];return s.jsx("header",{className:"w-full py-6 px-6 md:px-12 lg:px-24",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("nav",{className:"flex justify-between items-center",children:[s.jsx(o(),{href:"/",className:"flex items-center",children:s.jsx(n(),{src:"/images/logo.png",alt:"Navhaus",width:120,height:40,className:"h-8 w-auto -mt-[15px]"})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[l.map(e=>s.jsx(o(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${t===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,children:e.label},e.href)),s.jsx(o(),{href:"/contact",className:"btn-primary ml-8",children:"Start Project"})]}),s.jsx("button",{className:"md:hidden text-bauhaus-black",onClick:()=>r(!e),children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e?s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&s.jsx("div",{className:"md:hidden mt-6 py-6 border-t border-bauhaus-black",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[l.map(e=>s.jsx(o(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${t===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,onClick:()=>r(!1),children:e.label},e.href)),s.jsx(o(),{href:"/contact",className:"btn-primary inline-block mt-4",onClick:()=>r(!1),children:"Start Project"})]})})]})})}},3271:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(5036),a=t(7621),l=t(1843),o=t(3956),d=t(6193);function n({className:e="",color:r="red"}){return s.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} ${e}`,style:{borderRadius:"60% 40% 30% 70% / 60% 30% 70% 40%"}})}function i({className:e="",color:r="yellow",corner:t="top-left"}){return s.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]} ${{"top-left":"rounded-br-full","top-right":"rounded-bl-full","bottom-left":"rounded-tr-full","bottom-right":"rounded-tl-full"}[t]} ${e}`})}function c(){return(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("section",{className:"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden",children:[(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[s.jsx("h1",{className:"text-display font-bold mb-8",children:"Who We Are"}),s.jsx("p",{className:"text-xl md:text-2xl leading-relaxed text-gray-700",children:"Navhaus is a small, sharp team of designers and developers who cut through the noise. No fluff, no filler — just clear thinking and clean execution."})]}),s.jsx("div",{className:"absolute top-8 left-8 opacity-60",children:s.jsx(o.c9,{width:"sm",height:"lg",color:"blue"})}),s.jsx("div",{className:"absolute top-16 right-12 opacity-60",children:s.jsx(l.H,{size:"md",color:"yellow"})}),s.jsx("div",{className:"absolute bottom-8 right-8 opacity-40",children:s.jsx(n,{color:"red",className:"w-16 h-16"})})]}),(0,s.jsxs)("section",{className:"relative px-6 md:px-12 lg:px-24 py-16 md:py-24",children:[s.jsx("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12",children:[(0,s.jsxs)("div",{className:"relative bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-6",children:[s.jsx("div",{className:"absolute -top-4 -left-4",children:s.jsx(l.H,{size:"lg",color:"red"})}),s.jsx("h3",{className:"text-heading font-bold pt-4",children:"Clarity Over Complexity"}),s.jsx("p",{className:"text-body text-gray-700 leading-relaxed",children:"We believe good design is invisible. Simplicity scales."})]}),(0,s.jsxs)("div",{className:"relative bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-6",children:[s.jsx("div",{className:"absolute -top-4 -right-4",children:s.jsx(o.c9,{width:"lg",height:"md",color:"yellow"})}),s.jsx("h3",{className:"text-heading font-bold pt-4",children:"Small Team, Big Output"}),s.jsx("p",{className:"text-body text-gray-700 leading-relaxed",children:"You work directly with the people doing the work."})]}),(0,s.jsxs)("div",{className:"relative bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-6",children:[s.jsx("div",{className:"absolute -bottom-4 -left-4",children:s.jsx(d.Z,{size:"lg",color:"blue",direction:"up"})}),s.jsx("h3",{className:"text-heading font-bold",children:"Form Follows Function"}),s.jsx("p",{className:"text-body text-gray-700 leading-relaxed pb-4",children:"Every pixel, every line of code has a reason to exist."})]})]})}),s.jsx("div",{className:"absolute left-0 top-1/2 transform -translate-y-1/2 opacity-40",children:(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(o.c9,{width:"sm",height:"sm",color:"red"}),s.jsx(n,{color:"blue",className:"w-8 h-12"}),s.jsx(o.c9,{width:"sm",height:"sm",color:"yellow"})]})}),s.jsx("div",{className:"absolute right-8 bottom-8 opacity-40",children:s.jsx(i,{color:"red",corner:"top-left",className:"w-24 h-24"})})]})]})}},7621:(e,r,t)=>{"use strict";t.d(r,{Z:()=>b});var s=t(5036);let a=(0,t(6843).createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\layout\Header.tsx`),{__esModule:l,$$typeof:o}=a,d=a.default;var n=t(5904),i=t.n(n);function c(){return(0,s.jsxs)("footer",{className:"w-full py-12 px-6 md:px-12 lg:px-24 border-t border-bauhaus-black",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center space-y-6 md:space-y-0",children:[(0,s.jsxs)("div",{children:[s.jsx("div",{className:"mb-2",children:s.jsx(i(),{src:"/images/logo.png",alt:"Navhaus",width:100,height:32,className:"h-6 w-auto"})}),s.jsx("p",{className:"text-sm text-gray-600",children:"What matters, made real."})]}),(0,s.jsxs)("div",{className:"text-right",children:[s.jsx("p",{className:"text-sm font-medium mb-1",children:"Ready to build something?"}),s.jsx("a",{href:"mailto:<EMAIL>",className:"text-sm text-bauhaus-red hover:underline",children:"<EMAIL>"})]})]}),s.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["\xa9 ",new Date().getFullYear()," Navhaus. All rights reserved."]})})]})}function b({children:e,className:r=""}){return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col",children:[s.jsx(d,{}),s.jsx("main",{className:`flex-1 ${r}`,children:e}),s.jsx(c,{})]})}},1843:(e,r,t)=>{"use strict";t.d(r,{H:()=>d,Z:()=>o});var s=t(5036);let a={sm:"w-8 h-8",md:"w-16 h-16",lg:"w-24 h-24",xl:"w-32 h-32"},l={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function o({size:e="md",color:r="red",className:t=""}){return s.jsx("div",{className:`rounded-full ${a[e]} ${l[r]} ${t}`})}function d({size:e="md",color:r="red",className:t=""}){return s.jsx("div",{className:`rounded-full ${a[e]} ${l[r]} ${t}`,style:{filter:"blur(0.5px)"}})}},3956:(e,r,t)=>{"use strict";t.d(r,{ZP:()=>d,c9:()=>n});var s=t(5036);let a={sm:"w-12",md:"w-24",lg:"w-32",xl:"w-48"},l={sm:"h-8",md:"h-16",lg:"h-24",xl:"h-32"},o={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function d({width:e="md",height:r="md",color:t="blue",className:d=""}){return s.jsx("div",{className:`${a[e]} ${l[r]} ${o[t]} ${d}`})}function n({width:e="md",height:r="md",color:t="blue",className:d=""}){return s.jsx("div",{className:`rounded-3xl ${a[e]} ${l[r]} ${o[t]} ${d}`})}},6193:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(5036);let a={sm:"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]",md:"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]",lg:"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]",xl:"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]"},l=(e,r,t)=>{let s=a[e];switch(t){case"up":default:return`${s} border-l-transparent border-r-transparent`;case"down":return s.replace("border-b-","border-t-")+" border-l-transparent border-r-transparent";case"left":return s.replace("border-l-","border-r-").replace("border-r-","border-t-").replace("border-b-","border-l-")+" border-t-transparent border-b-transparent";case"right":return s.replace("border-r-","border-l-").replace("border-l-","border-t-").replace("border-b-","border-r-")+" border-t-transparent border-b-transparent"}};function o({size:e="md",color:r="yellow",direction:t="up",className:a=""}){let o=l(e,r,t);return s.jsx("div",{className:`${o} ${a}`,style:{borderBottomColor:"up"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderTopColor:"down"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderLeftColor:"right"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderRightColor:"left"===t?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent"}})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[847,515,904,462],()=>t(7915));module.exports=s})();