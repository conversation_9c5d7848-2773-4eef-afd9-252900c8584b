'use client'

import { useEffect } from 'react'

export default function BFCacheOptimizer() {
  useEffect(() => {
    // Only run optimizations in production
    if (process.env.NODE_ENV !== 'production') {
      return
    }

    // Handle page visibility changes to optimize bfcache
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // Clean up any ongoing operations before page goes to bfcache
        
        // Cancel any pending network requests
        if ('AbortController' in window) {
          // Store abort controllers globally if needed
          const controllers = (window as any).__abortControllers || []
          controllers.forEach((controller: AbortController) => {
            controller.abort()
          })
        }

        // Clear any timers that might prevent bfcache
        const highestTimeoutId = setTimeout(() => {}, 0)
        for (let i = 0; i < highestTimeoutId; i++) {
          clearTimeout(i)
        }

        const highestIntervalId = setInterval(() => {}, 0)
        for (let i = 0; i < highestIntervalId; i++) {
          clearInterval(i)
        }

        // Close any WebSocket connections (shouldn't exist in production)
        if ('WebSocket' in window) {
          // Check for any WebSocket instances and close them
          const webSockets = (window as any).__webSockets || []
          webSockets.forEach((ws: WebSocket) => {
            if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
              ws.close()
            }
          })
        }
      }
    }

    // Handle beforeunload to ensure clean exit
    const handleBeforeUnload = () => {
      // Ensure no blocking operations
      if ('navigator' in window && 'sendBeacon' in navigator) {
        // Use sendBeacon for any final analytics if needed
        // navigator.sendBeacon('/api/analytics', JSON.stringify({ event: 'page_unload' }))
      }
    }

    // Handle pagehide event for bfcache optimization
    const handlePageHide = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Page is being stored in bfcache
        console.debug('Page stored in bfcache')
      }
    }

    // Handle pageshow event for bfcache restoration
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Page was restored from bfcache
        console.debug('Page restored from bfcache')
        
        // Refresh any time-sensitive content
        const timeElements = document.querySelectorAll('[data-time-sensitive]')
        timeElements.forEach((element) => {
          // Trigger refresh of time-sensitive content
          const refreshEvent = new CustomEvent('bfcache-refresh')
          element.dispatchEvent(refreshEvent)
        })
      }
    }

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('pagehide', handlePageHide)
    window.addEventListener('pageshow', handlePageShow)

    // Optimize cache headers for bfcache compatibility
    const optimizeCacheHeaders = () => {
      // Check if we can modify response headers (limited in client-side)
      // This is mainly handled in next.config.js, but we can provide feedback
      
      if ('performance' in window && 'getEntriesByType' in performance) {
        const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
        
        navigationEntries.forEach((entry) => {
          // Log cache-related metrics for debugging
          console.debug('Navigation timing:', {
            type: entry.type,
            redirectCount: entry.redirectCount,
            transferSize: entry.transferSize,
            encodedBodySize: entry.encodedBodySize,
            decodedBodySize: entry.decodedBodySize,
          })
        })
      }
    }

    // Run cache optimization check
    optimizeCacheHeaders()

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pagehide', handlePageHide)
      window.removeEventListener('pageshow', handlePageShow)
    }
  }, [])

  // In development, show a warning about bfcache limitations
  if (process.env.NODE_ENV === 'development') {
    return (
      <div style={{ 
        position: 'fixed', 
        bottom: '10px', 
        right: '10px', 
        background: '#ff6b35', 
        color: 'white', 
        padding: '8px 12px', 
        borderRadius: '4px', 
        fontSize: '12px',
        zIndex: 9999,
        display: process.env.NODE_ENV === 'development' ? 'block' : 'none'
      }}>
        Dev Mode: BFCache disabled due to WebSocket
      </div>
    )
  }

  return null
}
