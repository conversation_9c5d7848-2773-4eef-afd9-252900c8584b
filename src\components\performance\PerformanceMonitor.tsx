'use client'

import { useEffect } from 'react'

export default function PerformanceMonitor() {
  useEffect(() => {
    // Enhanced performance monitoring for both dev and production
    const isDev = process.env.NODE_ENV === 'development'

    // Core Web Vitals monitoring
    const vitals = {
      lcp: 0,
      fid: 0,
      cls: 0,
      fcp: 0,
      ttfb: 0
    }

    // Monitor LCP (Largest Contentful Paint)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1] as any

      if (lastEntry) {
        vitals.lcp = Math.round(lastEntry.startTime)

        if (isDev) {
          console.log('🚀 LCP:', vitals.lcp, 'ms')
          console.log('LCP Element:', lastEntry.element)

          // LCP performance feedback
          if (vitals.lcp > 2500) {
            console.warn('⚠️  LCP is poor (>2.5s). Consider optimizing critical resources.')
          } else if (vitals.lcp > 1200) {
            console.log('⚡ LCP needs improvement (>1.2s)')
          } else {
            console.log('✅ LCP is good (<1.2s)')
          }
        }
      }
    })

    // Monitor FID (First Input Delay)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        vitals.fid = Math.round(entry.processingStart - entry.startTime)

        if (isDev) {
          console.log('👆 FID:', vitals.fid, 'ms')

          if (vitals.fid > 100) {
            console.warn('⚠️  FID is poor (>100ms). Consider reducing JavaScript execution time.')
          } else if (vitals.fid > 50) {
            console.log('⚡ FID needs improvement (>50ms)')
          } else {
            console.log('✅ FID is good (<50ms)')
          }
        }
      })
    })

    // Monitor CLS (Cumulative Layout Shift)
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      for (const entry of list.getEntries()) {
        const layoutShift = entry as any
        if (!layoutShift.hadRecentInput) {
          clsValue += layoutShift.value
        }
      }

      if (clsValue > 0) {
        vitals.cls = clsValue

        if (isDev) {
          console.log('📐 CLS:', vitals.cls.toFixed(4))

          if (vitals.cls > 0.25) {
            console.warn('⚠️  CLS is poor (>0.25). Check for layout shifts.')
          } else if (vitals.cls > 0.1) {
            console.log('⚡ CLS needs improvement (>0.1)')
          } else {
            console.log('✅ CLS is good (<0.1)')
          }
        }
      }
    })

    // Monitor FCP (First Contentful Paint)
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        vitals.fcp = Math.round(entry.startTime)

        if (isDev) {
          console.log('🎨 FCP:', vitals.fcp, 'ms')
        }
      })
    })

    // Monitor TTFB (Time to First Byte)
    const navigationObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        vitals.ttfb = Math.round(entry.responseStart - entry.requestStart)

        if (isDev) {
          console.log('🌐 TTFB:', vitals.ttfb, 'ms')

          if (vitals.ttfb > 600) {
            console.warn('⚠️  TTFB is slow (>600ms). Check server performance.')
          } else if (vitals.ttfb > 200) {
            console.log('⚡ TTFB could be faster (>200ms)')
          } else {
            console.log('✅ TTFB is good (<200ms)')
          }
        }
      })
    })

    // Start observing
    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      if (isDev) console.log('LCP monitoring not supported')
    }

    try {
      fidObserver.observe({ entryTypes: ['first-input'] })
    } catch (e) {
      if (isDev) console.log('FID monitoring not supported')
    }

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (e) {
      if (isDev) console.log('CLS monitoring not supported')
    }

    try {
      fcpObserver.observe({ entryTypes: ['first-contentful-paint'] })
    } catch (e) {
      if (isDev) console.log('FCP monitoring not supported')
    }

    try {
      navigationObserver.observe({ entryTypes: ['navigation'] })
    } catch (e) {
      if (isDev) console.log('Navigation timing not supported')
    }

    // Send vitals to analytics in production
    const sendVitals = () => {
      if (!isDev && 'navigator' in window && 'sendBeacon' in navigator) {
        // In a real app, you'd send this to your analytics service
        const data = JSON.stringify({
          url: window.location.href,
          vitals,
          timestamp: Date.now(),
          userAgent: navigator.userAgent
        })

        // Example: navigator.sendBeacon('/api/analytics/vitals', data)
        console.debug('Vitals collected:', vitals)
      }
    }

    // Send vitals when page is hidden
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        sendVitals()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup
    return () => {
      lcpObserver.disconnect()
      fidObserver.disconnect()
      clsObserver.disconnect()
      fcpObserver.disconnect()
      navigationObserver.disconnect()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return null
}
